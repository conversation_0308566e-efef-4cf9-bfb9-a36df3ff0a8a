version: 2
project_name: opencode
before:
  hooks:
builds:
  - env:
      - CGO_ENABLED=0
    goos:
      - linux
      - darwin
    goarch:
      - amd64
      - arm64
    ldflags:
      - -s -w -X github.com/opencode-ai/opencode/internal/version.Version={{.Version}}
    main: ./main.go

archives:
  - format: tar.gz
    name_template: >-
      opencode-
      {{- if eq .Os "darwin" }}mac-
      {{- else if eq .Os "windows" }}windows-
      {{- else if eq .Os "linux" }}linux-{{end}}
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "#86" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    format_overrides:
      - goos: windows
        format: zip
checksum:
  name_template: "checksums.txt"
snapshot:
  name_template: "0.0.0-{{ .Timestamp }}"
aurs:
  - name: opencode-ai
    homepage: "https://github.com/opencode-ai/opencode"
    description: "terminal based agent that can build anything"
    maintainers:
      - "kujti<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"
    license: "MIT"
    private_key: "{{ .Env.AUR_KEY }}"
    git_url: "ssh://<EMAIL>/opencode-ai-bin.git"
    provides:
      - opencode
    conflicts:
      - opencode
    package: |-
      install -Dm755 ./opencode "${pkgdir}/usr/bin/opencode"
brews:
  - repository:
      owner: opencode-ai
      name: homebrew-tap
nfpms:
  - maintainer: kujtimiihoxha
    description: terminal based agent that can build anything
    formats:
      - deb
      - rpm
    file_name_template: >-
      {{ .ProjectName }}-
      {{- if eq .Os "darwin" }}mac
      {{- else }}{{ .Os }}{{ end }}-{{ .Arch }}

changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^doc:"
      - "^test:"
      - "^ci:"
      - "^ignore:"
      - "^example:"
      - "^wip:"
