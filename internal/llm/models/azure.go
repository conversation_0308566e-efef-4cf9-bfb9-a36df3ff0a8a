package models

const ProviderAzure ModelProvider = "azure"

const (
	AzureGPT41        ModelID = "azure.gpt-4.1"
	AzureGPT41Mini    ModelID = "azure.gpt-4.1-mini"
	AzureGPT41Nano    ModelID = "azure.gpt-4.1-nano"
	AzureGPT45Preview ModelID = "azure.gpt-4.5-preview"
	AzureGPT4o        ModelID = "azure.gpt-4o"
	AzureGPT4oMini    ModelID = "azure.gpt-4o-mini"
	AzureO1           ModelID = "azure.o1"
	AzureO1Mini       ModelID = "azure.o1-mini"
	AzureO3           ModelID = "azure.o3"
	AzureO3Mini       ModelID = "azure.o3-mini"
	AzureO4Mini       ModelID = "azure.o4-mini"
)

var AzureModels = map[ModelID]Model{
	AzureGPT41: {
		ID:                  AzureGPT41,
		Name:                "Azure OpenAI – GPT 4.1",
		Provider:            ProviderAzure,
		APIModel:            "gpt-4.1",
		CostPer1MIn:         OpenAIModels[GPT41].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[GPT41].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[GPT41].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[GPT41].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[GPT41].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[GPT41].DefaultMaxTokens,
		SupportsAttachments: true,
	},
	AzureGPT41Mini: {
		ID:                  AzureGPT41Mini,
		Name:                "Azure OpenAI – GPT 4.1 mini",
		Provider:            ProviderAzure,
		APIModel:            "gpt-4.1-mini",
		CostPer1MIn:         OpenAIModels[GPT41Mini].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[GPT41Mini].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[GPT41Mini].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[GPT41Mini].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[GPT41Mini].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[GPT41Mini].DefaultMaxTokens,
		SupportsAttachments: true,
	},
	AzureGPT41Nano: {
		ID:                  AzureGPT41Nano,
		Name:                "Azure OpenAI – GPT 4.1 nano",
		Provider:            ProviderAzure,
		APIModel:            "gpt-4.1-nano",
		CostPer1MIn:         OpenAIModels[GPT41Nano].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[GPT41Nano].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[GPT41Nano].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[GPT41Nano].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[GPT41Nano].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[GPT41Nano].DefaultMaxTokens,
		SupportsAttachments: true,
	},
	AzureGPT45Preview: {
		ID:                  AzureGPT45Preview,
		Name:                "Azure OpenAI – GPT 4.5 preview",
		Provider:            ProviderAzure,
		APIModel:            "gpt-4.5-preview",
		CostPer1MIn:         OpenAIModels[GPT45Preview].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[GPT45Preview].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[GPT45Preview].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[GPT45Preview].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[GPT45Preview].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[GPT45Preview].DefaultMaxTokens,
		SupportsAttachments: true,
	},
	AzureGPT4o: {
		ID:                  AzureGPT4o,
		Name:                "Azure OpenAI – GPT-4o",
		Provider:            ProviderAzure,
		APIModel:            "gpt-4o",
		CostPer1MIn:         OpenAIModels[GPT4o].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[GPT4o].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[GPT4o].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[GPT4o].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[GPT4o].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[GPT4o].DefaultMaxTokens,
		SupportsAttachments: true,
	},
	AzureGPT4oMini: {
		ID:                  AzureGPT4oMini,
		Name:                "Azure OpenAI – GPT-4o mini",
		Provider:            ProviderAzure,
		APIModel:            "gpt-4o-mini",
		CostPer1MIn:         OpenAIModels[GPT4oMini].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[GPT4oMini].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[GPT4oMini].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[GPT4oMini].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[GPT4oMini].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[GPT4oMini].DefaultMaxTokens,
		SupportsAttachments: true,
	},
	AzureO1: {
		ID:                  AzureO1,
		Name:                "Azure OpenAI – O1",
		Provider:            ProviderAzure,
		APIModel:            "o1",
		CostPer1MIn:         OpenAIModels[O1].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[O1].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[O1].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[O1].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[O1].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[O1].DefaultMaxTokens,
		CanReason:           OpenAIModels[O1].CanReason,
		SupportsAttachments: true,
	},
	AzureO1Mini: {
		ID:                  AzureO1Mini,
		Name:                "Azure OpenAI – O1 mini",
		Provider:            ProviderAzure,
		APIModel:            "o1-mini",
		CostPer1MIn:         OpenAIModels[O1Mini].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[O1Mini].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[O1Mini].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[O1Mini].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[O1Mini].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[O1Mini].DefaultMaxTokens,
		CanReason:           OpenAIModels[O1Mini].CanReason,
		SupportsAttachments: true,
	},
	AzureO3: {
		ID:                  AzureO3,
		Name:                "Azure OpenAI – O3",
		Provider:            ProviderAzure,
		APIModel:            "o3",
		CostPer1MIn:         OpenAIModels[O3].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[O3].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[O3].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[O3].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[O3].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[O3].DefaultMaxTokens,
		CanReason:           OpenAIModels[O3].CanReason,
		SupportsAttachments: true,
	},
	AzureO3Mini: {
		ID:                  AzureO3Mini,
		Name:                "Azure OpenAI – O3 mini",
		Provider:            ProviderAzure,
		APIModel:            "o3-mini",
		CostPer1MIn:         OpenAIModels[O3Mini].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[O3Mini].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[O3Mini].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[O3Mini].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[O3Mini].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[O3Mini].DefaultMaxTokens,
		CanReason:           OpenAIModels[O3Mini].CanReason,
		SupportsAttachments: false,
	},
	AzureO4Mini: {
		ID:                  AzureO4Mini,
		Name:                "Azure OpenAI – O4 mini",
		Provider:            ProviderAzure,
		APIModel:            "o4-mini",
		CostPer1MIn:         OpenAIModels[O4Mini].CostPer1MIn,
		CostPer1MInCached:   OpenAIModels[O4Mini].CostPer1MInCached,
		CostPer1MOut:        OpenAIModels[O4Mini].CostPer1MOut,
		CostPer1MOutCached:  OpenAIModels[O4Mini].CostPer1MOutCached,
		ContextWindow:       OpenAIModels[O4Mini].ContextWindow,
		DefaultMaxTokens:    OpenAIModels[O4Mini].DefaultMaxTokens,
		CanReason:           OpenAIModels[O4Mini].CanReason,
		SupportsAttachments: true,
	},
}
